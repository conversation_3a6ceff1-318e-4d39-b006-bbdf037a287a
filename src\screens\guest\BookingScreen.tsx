import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Surface,
  Divider,
} from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';

import { useRoomStore } from '../../store/roomStore';
import { useReservationStore } from '../../store/reservationStore';
import { useAuthStore } from '../../store/authStore';
import { colors, spacing, typography } from '../../constants';
import { formatPrice } from '../../utils/currency';
import { CustomTextInput } from '../../components/ui/CustomTextInput';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';

interface RouteParams {
  roomId: string;
}

interface BookingForm {
  checkIn: Date;
  checkOut: Date;
  guests: number;
  specialRequests: string;
}

export const BookingScreen = () => {
  const route = useRoute();
  const navigation = useNavigation() as GuestNavigationProp;
  const { roomId } = route.params as RouteParams;
  
  const { user } = useAuthStore();
  const { selectedRoom, fetchRoomById, checkAvailability } = useRoomStore();
  const { createReservation, loading: reservationLoading } = useReservationStore();
  
  const [form, setForm] = useState<BookingForm>({
    checkIn: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    checkOut: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
    guests: 1,
    specialRequests: '',
  });
  
  const [showCheckInPicker, setShowCheckInPicker] = useState(false);
  const [showCheckOutPicker, setShowCheckOutPicker] = useState(false);
  const [availabilityChecked, setAvailabilityChecked] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const [errors, setErrors] = useState<Partial<BookingForm>>({});

  useEffect(() => {
    if (!selectedRoom || selectedRoom.id !== roomId) {
      fetchRoomById(roomId);
    }
  }, [roomId]);

  useEffect(() => {
    if (selectedRoom) {
      checkRoomAvailability();
    }
  }, [form.checkIn, form.checkOut, selectedRoom]);

  const checkRoomAvailability = async () => {
    if (!selectedRoom) return;

    setCheckingAvailability(true);
    setAvailabilityChecked(false);
    
    try {
      const available = await checkAvailability(
        selectedRoom.id,
        form.checkIn.toISOString().split('T')[0],
        form.checkOut.toISOString().split('T')[0]
      );
      
      setIsAvailable(available);
      setAvailabilityChecked(true);
    } catch (error) {
      console.error('Error checking availability:', error);
      setIsAvailable(false);
      setAvailabilityChecked(true);
    } finally {
      setCheckingAvailability(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<BookingForm> = {};
    
    // Check dates
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (form.checkIn < today) {
      newErrors.checkIn = new Date(); // Will show error in UI
      Alert.alert('Invalid Date', 'Check-in date cannot be in the past');
      return false;
    }
    
    if (form.checkOut <= form.checkIn) {
      newErrors.checkOut = new Date(); // Will show error in UI
      Alert.alert('Invalid Date', 'Check-out date must be after check-in date');
      return false;
    }
    
    // Check guests
    if (selectedRoom && form.guests > selectedRoom.max_occupancy) {
      Alert.alert(
        'Too Many Guests', 
        `This room can accommodate maximum ${selectedRoom.max_occupancy} guests`
      );
      return false;
    }
    
    if (form.guests < 1) {
      Alert.alert('Invalid Guests', 'At least 1 guest is required');
      return false;
    }

    // Check availability
    if (!availabilityChecked || !isAvailable) {
      Alert.alert('Room Not Available', 'Please select different dates');
      return false;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateTotal = () => {
    if (!selectedRoom) return 0;
    
    const nights = Math.ceil(
      (form.checkOut.getTime() - form.checkIn.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    return selectedRoom.price_per_night * nights;
  };



  const handleProceedToPayment = async () => {
    if (!validateForm() || !user || !selectedRoom) return;

    const reservationData = {
      room_id: selectedRoom.id,
      guest_id: user.id,
      check_in: form.checkIn.toISOString().split('T')[0],
      check_out: form.checkOut.toISOString().split('T')[0],
      guests: form.guests,
      total_amount: calculateTotal(),
      special_requests: form.specialRequests || null,
      status: 'pending' as const,
    };

    try {
      const result = await createReservation(reservationData);
      
      if (result.success && result.reservation) {
        navigation.navigate('Payment', { 
          reservationId: result.reservation.id,
          amount: calculateTotal()
        });
      } else {
        Alert.alert('Booking Error', result.error || 'Failed to create reservation');
      }
    } catch (error) {
      Alert.alert('Booking Error', 'An unexpected error occurred');
    }
  };

  if (!selectedRoom) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading room details...</Text>
      </View>
    );
  }

  const nights = Math.ceil(
    (form.checkOut.getTime() - form.checkIn.getTime()) / (1000 * 60 * 60 * 24)
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Room Summary */}
      <Card style={styles.roomCard}>
        <Card.Content>
          <Text style={styles.roomTitle}>Room {selectedRoom.room_number}</Text>
          <Text style={styles.roomType}>{selectedRoom.room_type}</Text>
          <Text style={styles.roomPrice}>
            {formatPrice(selectedRoom.price_per_night)}/night
          </Text>
        </Card.Content>
      </Card>

      {/* Booking Form */}
      <Card style={styles.formCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Booking Details</Text>
          
          {/* Check-in Date */}
          <View style={styles.dateContainer}>
            <Text style={styles.dateLabel}>Check-in Date</Text>
            <Button
              mode="outlined"
              onPress={() => setShowCheckInPicker(true)}
              style={styles.dateButton}
              contentStyle={styles.dateButtonContent}
              labelStyle={styles.dateButtonLabel}
            >
              {form.checkIn.toLocaleDateString()}
            </Button>
          </View>

          {/* Check-out Date */}
          <View style={styles.dateContainer}>
            <Text style={styles.dateLabel}>Check-out Date</Text>
            <Button
              mode="outlined"
              onPress={() => setShowCheckOutPicker(true)}
              style={styles.dateButton}
              contentStyle={styles.dateButtonContent}
              labelStyle={styles.dateButtonLabel}
            >
              {form.checkOut.toLocaleDateString()}
            </Button>
          </View>

          {/* Number of Guests */}
          <View style={styles.guestsContainer}>
            <Text style={styles.guestsLabel}>Number of Guests</Text>
            <View style={styles.guestsControls}>
              <Button
                mode="outlined"
                onPress={() => setForm(prev => ({ 
                  ...prev, 
                  guests: Math.max(1, prev.guests - 1) 
                }))}
                disabled={form.guests <= 1}
                compact
              >
                -
              </Button>
              <Text style={styles.guestsCount}>{form.guests}</Text>
              <Button
                mode="outlined"
                onPress={() => setForm(prev => ({ 
                  ...prev, 
                  guests: Math.min(selectedRoom.max_occupancy, prev.guests + 1) 
                }))}
                disabled={form.guests >= selectedRoom.max_occupancy}
                compact
              >
                +
              </Button>
            </View>
          </View>
          <Text style={styles.maxGuestsText}>
            Maximum {selectedRoom.max_occupancy} guests allowed
          </Text>

          {/* Special Requests */}
          <CustomTextInput
            label="Special Requests (Optional)"
            value={form.specialRequests}
            onChangeText={(text) => setForm(prev => ({ ...prev, specialRequests: text }))}
            multiline
            numberOfLines={3}
            style={styles.specialRequestsInput}
          />
        </Card.Content>
      </Card>

      {/* Availability Status */}
      <Surface style={styles.availabilityCard}>
        <View style={styles.availabilityContent}>
          {checkingAvailability ? (
            <>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.availabilityText}>Checking availability...</Text>
            </>
          ) : availabilityChecked ? (
            <>
              <MaterialIcons
                name={isAvailable ? "check-circle" : "cancel"}
                size={24}
                color={isAvailable ? colors.success : colors.error}
              />
              <Text style={[
                styles.availabilityText,
                { color: isAvailable ? colors.success : colors.error }
              ]}>
                {isAvailable ? 'Room is available' : 'Room is not available for selected dates'}
              </Text>
            </>
          ) : (
            <>
              <MaterialIcons name="info" size={24} color={colors.info} />
              <Text style={styles.availabilityText}>Select dates to check availability</Text>
            </>
          )}
        </View>
      </Surface>

      {/* Booking Summary */}
      <Card style={styles.summaryCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Booking Summary</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Check-in:</Text>
            <Text style={styles.summaryValue}>
              {form.checkIn.toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Check-out:</Text>
            <Text style={styles.summaryValue}>
              {form.checkOut.toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Nights:</Text>
            <Text style={styles.summaryValue}>{nights}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Guests:</Text>
            <Text style={styles.summaryValue}>{form.guests}</Text>
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>
              {formatPrice(selectedRoom.price_per_night)} × {nights} night{nights !== 1 ? 's' : ''}:
            </Text>
            <Text style={styles.summaryValue}>
              {formatPrice(calculateTotal())}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total:</Text>
            <Text style={styles.totalValue}>
              {formatPrice(calculateTotal())}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Proceed Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          onPress={handleProceedToPayment}
          disabled={!availabilityChecked || !isAvailable || reservationLoading}
          style={[
            styles.proceedButton,
            {
              backgroundColor: (!availabilityChecked || !isAvailable || reservationLoading)
                ? '#9CA3AF'
                : '#2E8B57',
            }
          ]}
          activeOpacity={0.8}
        >
          <View style={styles.buttonContent}>
            {reservationLoading && (
              <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
            )}
            <Text style={styles.proceedButtonText}>
              {reservationLoading ? 'Processing...' : 'Proceed to Payment'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Date Pickers */}
      {showCheckInPicker && (
        <DateTimePicker
          value={form.checkIn}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowCheckInPicker(false);
            if (selectedDate) {
              setForm(prev => ({ ...prev, checkIn: selectedDate }));
            }
          }}
          minimumDate={new Date()}
        />
      )}

      {showCheckOutPicker && (
        <DateTimePicker
          value={form.checkOut}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowCheckOutPicker(false);
            if (selectedDate) {
              setForm(prev => ({ ...prev, checkOut: selectedDate }));
            }
          }}
          minimumDate={new Date(form.checkIn.getTime() + 24 * 60 * 60 * 1000)}
        />
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...typography.body,
    marginTop: spacing.md,
    color: colors.textSecondary,
    fontSize: 16,
    fontWeight: '500',
  },
  roomCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
  },
  roomTitle: {
    ...typography.h3,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.sm,
    fontSize: 24,
  },
  roomType: {
    ...typography.subtitle,
    color: colors.textPrimary,
    textTransform: 'capitalize',
    marginBottom: spacing.sm,
    fontSize: 18,
    fontWeight: '600',
  },
  roomPrice: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: 20,
  },
  formCard: {
    margin: spacing.md,
    marginVertical: spacing.sm,
  },
  sectionTitle: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.md,
    fontSize: 20,
  },
  dateContainer: {
    marginBottom: spacing.md,
  },
  dateLabel: {
    ...typography.body,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
    fontSize: 16,
    fontWeight: '600',
  },
  dateButton: {
    justifyContent: 'flex-start',
    borderRadius: 8,
  },
  dateButtonContent: {
    justifyContent: 'flex-start',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  dateButtonLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
  },
  guestsContainer: {
    marginBottom: spacing.sm,
  },
  guestsLabel: {
    ...typography.body,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
    fontSize: 16,
    fontWeight: '600',
  },
  guestsControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  guestsCount: {
    ...typography.h4,
    fontWeight: 'bold',
    minWidth: 30,
    textAlign: 'center',
    fontSize: 20,
    color: colors.textPrimary,
  },
  maxGuestsText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    fontSize: 14,
    fontWeight: '400',
  },
  specialRequestsInput: {
    marginTop: spacing.sm,
  },
  availabilityCard: {
    margin: spacing.md,
    marginVertical: spacing.sm,
    padding: spacing.md,
    borderRadius: 8,
    elevation: 2,
  },
  availabilityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  availabilityText: {
    ...typography.body,
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
  },
  summaryCard: {
    margin: spacing.md,
    marginVertical: spacing.sm,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    ...typography.body,
    color: colors.textPrimary,
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
  },
  summaryValue: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    fontSize: 16,
  },
  divider: {
    marginVertical: spacing.md,
  },
  totalRow: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalLabel: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.textPrimary,
    fontSize: 20,
  },
  totalValue: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: 20,
  },
  buttonContainer: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  proceedButton: {
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  proceedButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
});
